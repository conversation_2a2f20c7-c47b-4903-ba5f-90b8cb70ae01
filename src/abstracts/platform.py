from abc import ABC, abstractmethod
from ..models.sqlmodels import S<PERSON><PERSON>ield
from ..mongocontext.dbcontext import *
from tenacity import retry, stop_after_attempt, wait_exponential


class PlatformClient(ABC):
    def __init__(self, platform_config: PlatformConfig) -> None:
        self.platform_config = platform_config

    @abstractmethod
    def authenticate_connector(self, config: ConnectorConfig) -> ConnectorConfig:
        pass

    def create_table(self, config: ConnectorConfig, schema: list[SQLField]):
        pass

    def get_access_token(self, config: ConnectorConfig) -> str:
        pass

    @abstractmethod
    def handle_message(self, config: ConnectorConfig, **kwargs) -> bool:
        pass

    async def update_access_token(self, config: ConnectorConfig) -> None:
        db_context = DbContext()
        await db_context.initialize_client()
        connector_info = await db_context.get_connector(config.name)
        connector_info.config = config
        await db_context.update_connector(connector_info)

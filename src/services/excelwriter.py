import json
import os.path
import openpyxl
from openpyxl.styles import Alignment, Font, PatternFill, Border, Side
from openpyxl.utils import get_column_letter


def format_array_data(arr):
    """Format array data for Excel display, joining with newlines"""
    if not arr:
        return ""
    return "\n".join(str(item) for item in arr)


def format_phone(phone_num):
    """Format a phone number to (xxx) xxx-xxxx if possible"""
    if phone_num == 1234567890:  # Skip placeholder number
        return ""

    phone_str = str(phone_num)
    if len(phone_str) == 10:  # Format as (xxx) xxx-xxxx
        return f"({phone_str[0:3]}) {phone_str[3:6]}-{phone_str[6:10]}"
    return phone_str


def clean_text(text):
    """Clean text by removing control characters"""
    if not text:
        return ""
    if isinstance(text, str):
        # Remove control characters (0x00-0x1F and 0x7F-0x9F)
        return ''.join(char for char in text if ord(char) >= 32 and (ord(char) < 127 or ord(char) > 159))
    return str(text)


def create_excel_from_json(json_data, output_file):
    """Create Excel file from art agencies JSON data with proper cell merges"""

    # Create a new workbook and select the active worksheet
    wb = openpyxl.Workbook()
    agencies_ws = wb.active
    agencies_ws.title = "Agencies"

    # Create a second worksheet for contacts
    contacts_ws = wb.create_sheet("Contacts")

    # Define styles
    header_font = Font(bold=True)
    header_fill = PatternFill(start_color="ADD8E6", end_color="ADD8E6", fill_type="solid")  # Light Blue fill
    thin_border = Border(left=Side(style='thin'), right=Side(style='thin'),
                         top=Side(style='thin'), bottom=Side(style='thin'))
    light_grey_fill = PatternFill(start_color="D3D3D3", end_color="D3D3D3", fill_type="solid")  # Light grey fill
    red_font = Font(color="FF0000")  # Red font color

    # Set up headers for agencies worksheet
    agency_headers = ["Agency Name", "Website", "Email", "Phone", "Mailing Address", "Physical Address", "Jurisdiction",
                      "Organization Type", "Socials", "Information Pending"]
    for col_idx, header in enumerate(agency_headers, 1):
        cell = agencies_ws.cell(row=1, column=col_idx)
        cell.value = header
        cell.font = header_font
        cell.fill = header_fill
        cell.alignment = Alignment(horizontal="center", vertical="center", wrap_text=True)
        cell.border = thin_border

    # Set up headers for contacts worksheet
    contact_headers = ["Agency Name", "Contact Name", "Role", "Email", "Phone", "Mailing Address", "Physical Address"]
    for col_idx, header in enumerate(contact_headers, 1):
        cell = contacts_ws.cell(row=1, column=col_idx)
        cell.value = header
        cell.font = header_font
        cell.fill = header_fill
        cell.alignment = Alignment(horizontal="center", vertical="center", wrap_text=True)
        cell.border = thin_border

    # Process agencies data
    agency_row = 2  # Start from row 2 after headers
    contact_row = 2  # Start from row 2 after headers
    contact_group_count = 1

    agency_header_count = len(agency_headers)
    contact_header_count = len(contact_headers)

    for idx, agency in enumerate(json_data):
        # Add agency data to agencies worksheet
        agencies_ws.cell(row=agency_row, column=1).value = clean_text(agency["AgencyName"])
        agencies_ws.cell(row=agency_row, column=2).value = clean_text(agency["AgencyUrl"])
        agencies_ws.cell(row=agency_row, column=3).value = clean_text(format_array_data(agency["Email"]))

        # Format phone numbers
        formatted_phones = [format_phone(phone) for phone in agency["Phone"] if format_phone(phone)]
        agencies_ws.cell(row=agency_row, column=4).value = clean_text(format_array_data(formatted_phones))

        agencies_ws.cell(row=agency_row, column=5).value = clean_text(format_array_data(agency["MailingAddress"]))
        agencies_ws.cell(row=agency_row, column=6).value = clean_text(format_array_data(agency["PhysicalAddress"]))
        agencies_ws.cell(row=agency_row, column=7).value = agency["Jurisdiction"]
        agencies_ws.cell(row=agency_row, column=8).value = agency["OrganizationType"]
        agencies_ws.cell(row=agency_row, column=9).value = format_array_data(agency["Socials"])
        agencies_ws.cell(row=agency_row, column=10).value = "Yes" if agency["InformationPending"] else "No"

        # Apply styling to agency cells
        for col in range(1, agency_header_count+1):
            cell = agencies_ws.cell(row=agency_row, column=col)
            cell.alignment = Alignment(vertical="top", wrap_text=True)
            cell.border = thin_border

        for row_idx in range(agency_row, agency_row + 1):
            for col_idx in range(1, agency_header_count+1):  # Columns A to G
                cell = agencies_ws.cell(row=row_idx, column=col_idx)
                if idx % 2 != 0:  # Check if agency index is odd
                    cell.fill = light_grey_fill
                if agency["InformationPending"]:
                    cell.font = red_font

        agency_row += 1

        # Process contact persons for this agency
        contacts = agency["ContactPerson"]
        start_contact_row = contact_row
        if contacts:
            contact_group_count += 1

        for contact in contacts:
            # Add agency name to contacts worksheet (will be merged later if multiple contacts)
            contacts_ws.cell(row=contact_row, column=1).value = clean_text(agency["AgencyName"])

            # Add contact details
            contacts_ws.cell(row=contact_row, column=2).value = clean_text(contact.get("ContactName", ""))
            contacts_ws.cell(row=contact_row, column=3).value = clean_text(contact.get("ContactRole", ""))
            contacts_ws.cell(row=contact_row, column=4).value = clean_text(format_array_data(contact.get("Email", [])))

            # Format contact phone numbers
            formatted_contact_phones = [format_phone(phone) for phone in contact.get("Phone", [])
                                        if format_phone(phone)]

            contacts_ws.cell(row=contact_row, column=5).value = clean_text(format_array_data(formatted_contact_phones))
            contacts_ws.cell(row=contact_row, column=6).value = clean_text(
                format_array_data(contact.get("MailingAddress", [])))
            contacts_ws.cell(row=contact_row, column=7).value = clean_text(
                format_array_data(contact.get("PhysicalAddress", [])))

            # Apply styling to contact cells
            for col in range(1, contact_header_count+1):
                cell = contacts_ws.cell(row=contact_row, column=col)
                cell.alignment = Alignment(vertical="top", wrap_text=True)
                cell.border = thin_border

            contact_row += 1

        # Merge agency name cells in contacts worksheet if there are multiple contacts
        end_contact_row = contact_row - 1

        # Apply alternating row color to contact rows
        if contact_group_count % 2 != 0:  # Check if contact group index is odd
            for row_idx in range(start_contact_row, end_contact_row + 1):
                for col_idx in range(1, contact_header_count+1):  # Columns A to G
                    cell = contacts_ws.cell(row=row_idx, column=col_idx)
                    cell.fill = light_grey_fill

        if end_contact_row > start_contact_row:
            contacts_ws.merge_cells(
                start_row=start_contact_row,
                start_column=1,
                end_row=end_contact_row,
                end_column=1
            )

            # Center the merged cell vertically
            merged_cell = contacts_ws.cell(row=start_contact_row, column=1)
            merged_cell.alignment = Alignment(vertical="center", wrap_text=True)

    # Auto-adjust column widths for both worksheets
    for worksheet in [agencies_ws, contacts_ws]:
        for col in worksheet.columns:
            max_length = 0
            column = col[0].column_letter
            for cell in col:
                if cell.value:
                    # Consider multiple lines and take the longest
                    if isinstance(cell.value, str):
                        lines = cell.value.split('\n')
                        max_line_length = max(len(line) for line in lines)
                        if max_line_length > max_length:
                            max_length = max_line_length
                    else:
                        cell_length = len(str(cell.value))
                        if cell_length > max_length:
                            max_length = cell_length

            # Set width with some padding
            adjusted_width = max_length + 4
            worksheet.column_dimensions[column].width = min(adjusted_width, 50)  # Cap at 50

    # Save the workbook
    wb.save(output_file)
    print(f"Excel file created: {output_file}")

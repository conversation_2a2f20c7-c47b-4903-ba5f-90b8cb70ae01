from fastapi import Request
from src.services.logger import logger


async def log_middleware(request: Request, call_next):
    log = {
        'url': request.url.path,
        'method': request.method,
        'request': await request.body()
    }
    try:
        logger.info(log)
        response = await call_next(request)
        return response
    except Exception as exc:
        logger.critical(exc, exc_info=True)
        return exc

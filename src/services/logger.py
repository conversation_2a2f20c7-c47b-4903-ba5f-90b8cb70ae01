from loki_logger_handler.loki_logger_handler import <PERSON><PERSON>ogger<PERSON><PERSON><PERSON>
from loki_logger_handler.formatters.loguru_formatter import <PERSON><PERSON><PERSON><PERSON><PERSON>atter
from loguru import logger as llogger
from src.appsettings import *
import sys
from functools import wraps
from time import time
import inspect
import traceback


class LokiLogger:
    def __init__(self):
        self.base_url = f"http://{LOG_SERVER}/loki/api/v1/push"
        self.labels = {"application": "pyconnectors:0.1.0_py3.12.7"}
        self.logger = llogger

    def get_logger(self):
        self.logger.handlers = []
        # self.set_file_handler()
        self.set_loki_handler()
        # self.set_console_handler()
        return self.logger

    def set_loki_handler(self):
        loki_handler = LokiLoggerHandler(
            url=self.base_url,
            labels=self.labels,
            label_keys={},
            timeout=10,
            default_formatter=LoguruFormatter()
        )
        self.logger.add(loki_handler, level=LOG_LEVEL, serialize=True)

    # def set_file_handler(self):
    #     file_format = "%(asctime)s - %(levelname)s - %(pathname)s - %(funcName)s - %(message)s"
    #     formatter = logging.Formatter(fmt=file_format)
    #     handler = logging.FileHandler(f'logs/{datetime.now().strftime("%Y-%m")}.log')
    #     handler.setFormatter(formatter)
    #     self.logger.addHandler(handler)

    # def set_console_handler(self):
    #     console_format = "%(asctime)s - %(levelname)s - %(pathname)s - %(funcName)s - %(message)s"
    #     formatter = logging.Formatter(fmt=console_format)
    #     handler = logging.StreamHandler(sys.stdout)
    #     handler.setFormatter(formatter)
    #     self.logger.addHandler(handler)


class debug_context:
    def __init__(self, name: str, debug_info: dict):
        self.name = name
        self.prev_locals = {}
        self.debug_info = debug_info
        self.arguments = {}
        self.locals = []
        self.count = 0

    def __enter__(self):
        sys.settrace(self.trace_calls)

    def __exit__(self, *args, **kwargs):
        sys.settrace(None)

    def trace_calls(self, frame, event, arg):
        if event != 'call':
            return
        elif frame.f_code.co_name != self.name:
            return
        return self.trace_lines

    def trace_lines(self, frame, event, arg):
        if event not in ['line', 'return']:
            return

        local_vars = frame.f_locals
        line_no = frame.f_lineno - 1

        if self.count == 0:
            self.prev_locals = {k: v for k, v in local_vars.items()}
            self.arguments = {k: {'value':  v, 'line': line_no} for k, v in local_vars.items() if k not in ['self', 'cls']}
            self.count += 1
            return

        co = frame.f_code
        filename = co.co_filename

        new_vars = {k: v for k, v in local_vars.items() if k not in self.prev_locals or self.prev_locals[k] != v}
        self.prev_locals = local_vars.copy()

        if new_vars:
            new_vars.pop('self', None)
            frame_vars = {k: {'value': v, 'line': line_no} for k, v in new_vars.items()}
            self.locals.append(frame_vars)

        if event == 'return':
            self.debug_info['location'] = filename
            self.debug_info['args'] = self.arguments
            self.debug_info['locals'] = self.locals


logger = LokiLogger().get_logger()

def elasticlog(config=None):
    if callable(config):  # No arguments passed, `config` is actually the function
        method = config
        config = {}
        return _elasticlog_decorator(method, config)
    def wrapper(method):
        return _elasticlog_decorator(method, config or {})
    return wrapper


def _elasticlog_decorator(method, config):
    @wraps(method)
    def elastic_logging(*args, **kwargs):
        start = time()
        extras = {'method': method.__qualname__}
        if config:
            extras.update(config)
        logger.configure(extra=extras)
        log = {}
        debug_info = {}
        try:
            if LOG_LEVEL >= 30:
                result = method(*args, **kwargs)
            elif LOG_LEVEL >= 20:
                result = method(*args, **kwargs)
                method_args = inspect.signature(method).bind(*args, **kwargs).arguments
                debug_info = {'args': {k: v for k, v in method_args.items() if k not in ['self', 'cls']},
                              'return': locals()['result']}
            else:
                with debug_context(method.__name__, debug_info):
                    result = method(*args, **kwargs)
                    debug_info['return'] = result
            debug_info['execution_ms'] = (time() - start) * 1000
            log = {"info": debug_info}
            logger.info(log)
            logger.complete()
            return result
        except Exception as e:
            ex_type, ex, ex_traceback = sys.exc_info()
            debug_info['execution_ms'] = (time() - start) * 1000
            log.update({
                "info": debug_info,
                "ex_message": str(e),
                "ex_type": ex_type.__name__,
                "ex_traceback": traceback.format_tb(ex_traceback)
            })
            logger.error(log)
            logger.complete()
            raise e
    return elastic_logging

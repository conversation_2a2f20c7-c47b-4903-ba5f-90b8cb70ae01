from apify_client import Apify<PERSON><PERSON>
from src.appsettings import APIFY_API_TOKEN

class ApifyService:
    _instance = None

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(ApifyService, cls).__new__(cls)
            # Initialize instance variables but not connections
            cls._instance._client = None
        return cls._instance

    def __init__(self):
        # Constructor does nothing - initialization happens in __new__
        pass

    @property
    def client(self):
        if self._client is None:
            self._client = ApifyClient(APIFY_API_TOKEN)
        return self._client

    def run_actor(self, url: str):
        run_input = {
            "startUrls": [
                {"url": url}
            ]
        }
        run = self.client.actor("4Hv5RhChiaDk6iwad").call(run_input=run_input)
        data = [item for item in self.client.dataset(run["defaultDatasetId"]).iterate_items()][0]

        return run
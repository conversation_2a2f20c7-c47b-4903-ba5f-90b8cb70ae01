import asyncio
from xmlrpc.client import Server<PERSON>roxy
from src.models.connectormodels import Connector<PERSON>onfig, ConnectorInfo
from src.appsettings import BG_WORKER_SERVER
from src.common.helper import *
from src.services.logger import logger, elasticlog
from src.worker.workerexceptions import *
from tenacity import retry, stop_after_attempt, wait_exponential


class BackgroundWorker:
    def __init__(self) -> None:
        self.server = ServerProxy(BG_WORKER_SERVER)
        self.connector_group = self.server.connector

    async def startup(self, connector_info: ConnectorInfo) -> ConnectorInfo:
        if connector_info.statename == "RUNNING":
            conn_info = await self.add_connector(connector_info.config)
        else:
            conn_info = await self.add_connector(connector_info.config, "false", "true")
        conn_info.config = connector_info.config
        conn_info.id = connector_info.id
        return conn_info

    @elasticlog
    async def add_connector(self, config: ConnectorConfig,
                            autostart: str = "true",
                            autorestart: str = "false") -> ConnectorInfo:
        args = create_arguments(config.__dict__)
        logger.info(args)
        platform_type = config.platform_type if config.source_platform is None else config.source_platform
        self.connector_group.addProgramToGroup("connectors", config.name,
                                               {"command": f"python -u "
                                                           f"{platform_type}/{config.connector_type}.py "
                                                           f"{args}",
                                                "autostart": autostart.lower(),
                                                "autorestart": autorestart.lower(),
                                                "exitcodes":"0",
                                                "startretries":"3",
                                                "stopsignal": "INT",
                                                "stopwaitsecs": "10",
                                                "stderr_logfile": f"/var/log/{config.name}.err.log",
                                                "stdout_logfile": f"/var/log/{config.name}.out.log"})
        return await self.get_connector_info(config.name)

    async def stop_connector(self, connector_name: str, current_state: str) -> ConnectorInfo:
        if current_state == "RUNNING":
            self.server.supervisor.stopProcess(f"connectors:{connector_name.lower()}")
        return await self.get_connector_info(connector_name)

    async def start_connector(self, connector_name: str) -> ConnectorInfo:
        self.server.supervisor.startProcess(f"connectors:{connector_name.lower()}")
        return await self.get_connector_info(connector_name)

    async def delete_connector(self, connector_name: str, current_state: str) -> None:
        try:
            await self.stop_connector(connector_name, current_state)
        except Exception as e:
            logger.warning(f"Failed to stop connector {connector_name}, possibly cause connector was NOT in running state.", exc_info=True)
            pass
        self.connector_group.removeProcessFromGroup("connectors", connector_name.lower())

    @retry(stop=stop_after_attempt(5), wait=wait_exponential(multiplier=1, min=1, max=5))
    async def get_connector_info(self, connector_name: str) -> ConnectorInfo:
        process_info = self.server.supervisor.getProcessInfo(f'connectors:{connector_name.lower()}')
        if process_info["statename"] != "RUNNING":
            raise BackgroundWorkerException(f"Connector {connector_name.lower()} failed to start. Please check the logs.")
        process_info['start'] = int_to_datestr(process_info['start'])
        process_info['stop'] = int_to_datestr(process_info['stop'])
        process_info['now'] = int_to_datestr(process_info['now'])
        return ConnectorInfo(**process_info)

from src.contacts.models import *
import openai
import json
import re
import os
from dataclasses import asdict
from typing import List, Dict, Any, Optional
from pydantic import ValidationError

class OpenAIService:
    def __init__(self) -> None:
        api_key = os.getenv("OPENAI_API_KEY")
        if not api_key:
            raise ValueError("Missing OpenAI API Key. Ensure it's set in the .env file.")
        self.client = openai.OpenAI(api_key=api_key)
        self.target_schema = json.dumps({
            "AgencyName": "string (use provided)",
            "AgencyUrl": "string (use provided)",
            "Email": "list[string] (valid emails, unique)",
            "Phone": "list[integer] (valid phone numbers, unique)",
            "Address": "list[string] (valid PO Box or street addresses, unique)",
            "Socials": "list[string] (valid social media URLs, unique)",
            "ContactPerson": [
                {
                    "ContactName": "string (full name)",
                    "ContactRole": "string | null (job or role title)",
                    "Email": "list[string] (valid emails, unique)",
                    "Phone": "list[integer] (valid phone numbers, unique)",
                    "Address": "list[string] (valid PO Box or street addresses, unique)",
                    "Socials": "list[string] (valid social media URLs, unique)"
                }
            ]
        }, indent=2)
        self.social_keywords = ["facebook.com", "x.com", "twitter.com", "instagram.com", "linkedin.com"]

    def fetch_contact_info(self, company, url, html) -> List[Dict[str, Any]]:
        system_message = {
            "role": "system",
            "content": (
                "You are a strict JSON extractor. Follow the instructions exactly and output only a valid JSON list with no extra text."
            )
        }
        user_message = {
            "role": "user",
            "content": f"""Extract all available contact details from the HTML text related to {company} at {url}.
            Return a JSON list of flat objects. Each object must include:
            - "type": one of ["email", "phone", "address", "name", "role", "socials"]
            - "value": the extracted information (for phone numbers, include only digits)
            - "person_name": optional; include if the detail is clearly tied to a specific person.
    
            Rules:
            1. Extract all identifiable contact information.
            2. Every person with a name must have a corresponding "role" object if available.
            3. Always associate roles (job titles) with the corresponding person’s name by creating separate "name" and "role" objects linked by "person_name".
            4. If an email or phone number is tied to an individual, include "person_name"; omit it for general contact info.
            5. Return phone numbers as strings containing only digits (no spaces, dashes, or parentheses).
            6. Addresses may include street addresses, PO Box numbers, or other identifiable location descriptions.
            7. For "socials", extract only URLs for Facebook, Twitter/X, Instagram, and LinkedIn. Each URL should be in its own object with "type": "socials" and include "person_name" if applicable.
            8. Output ONLY the JSON list without any additional text or explanation.
            9. If no contact information is found, output [].
    
            HTML:
            ```html
            {html}
            ```"""
        }

        try:
            completion = self.client.chat.completions.create(
                model="gpt-4o-mini",
                messages=[system_message, user_message],
                temperature=0,
                stop=["\n\n"],
                response_format={"type": "json_object"} # Note: Expecting a dict containing the list
            )

            # Assuming the response format might wrap the list in a key, e.g., {"contacts": [...]}
            # Or it might return the list directly if the model understands the prompt well.
            response_content = completion.choices[0].message.content.strip()
            if not response_content:
                print("⚠️ OpenAI returned an empty response.")
                return []

            extracted_data = json.loads(response_content)

            # Handle potential wrapping dictionary { "key": [...] }
            if isinstance(extracted_data, dict):
                # Try to find a key whose value is a list
                list_value = next((v for v in extracted_data.values() if isinstance(v, list)), None)
                if list_value is not None:
                    extracted_items = list_value
                else:
                    print(f"⚠️ OpenAI returned a dict, but no list found inside: {extracted_data}")
                    return []
            elif isinstance(extracted_data, list):
                extracted_items = extracted_data
            else:
                print(f"⚠️ OpenAI returned unexpected format: {type(extracted_data)}")
                return []

            # Basic validation of list items
            validated_items = []
            for item in extracted_items:
                if isinstance(item, dict) and 'type' in item and 'value' in item:
                    validated_items.append(item)
                else:
                     print(f"⚠️ Skipping invalid item in response: {item}")
            
            return validated_items

        except json.JSONDecodeError:
            print(f"⚠️ JSON Parsing Error! Raw Response: {response_content}")
            return []
        except openai.APIError as e:
            print(f"❌ OpenAI API Error: {e}")
            return []
        except Exception as e:
            print(f"❌ Unexpected Error processing OpenAI response: {e}")
            print(f"Raw response causing error: {response_content if 'response_content' in locals() else 'N/A'}")
            return []

    def clean_and_structure_contact_info(self, agency_name: str, agency_url: str, raw_items: List[Dict[str, Any]]) -> Optional[ContactDetail]:
        if not raw_items:
            print("No raw items to clean.")
            return ContactDetail(AgencyName=agency_name, AgencyUrl=agency_url, InformationPending=True)

        raw_items_json = json.dumps(raw_items, indent=2)

        system_message = {
            "role": "system",
            "content": "You are a strict JSON extractor and data cleaner. Follow the instructions exactly and output only valid JSON that matches the target schema."
        }

        user_message = {
            "role": "user",
            "content": f"""You are provided with a raw JSON list of contact information items extracted from the website for "{agency_name}" ({agency_url}). 
            Your task is to clean, validate, deduplicate, and structure this information into a single final JSON object that EXACTLY matches the target schema below.
            
            Target Schema:
            ```json
            {self.target_schema}
            ```

            Raw JSON List:
            ```json
            {raw_items_json}
            ```

            Cleaning and Structuring Rules:
            1. Set AgencyName to "{agency_name}" and AgencyUrl to "{agency_url}".
            2. Aggregate all information and remove exact duplicates within each list (for the agency and each person).
            3. Validate emails: include only valid email addresses.
            4. Validate phones: ensure numbers contain only digits; convert valid numbers to integers and discard invalid entries.
            5. Validate and format addresses:
                - Identify valid mailing (PO Box) or physical (street) addresses.
                - Discard entries that are purely numeric or are URLs.
                - Combine split address fragments into a single complete address. (e.g., "P.O. Box 432" and "Brewton, AL 36427" should be combined into "P.O. Box 432, Brewton, AL 36427").
                - Normalize addresses by removing extra whitespace and standardizing common abbreviations using standard postal formats. (e.g., St to Street, Ave./Ave to Avenue) using standard postal formats.
                - If multiple normalized addresses refer to the same location, keep only the most complete version.
            6. Group information by person_name; create one object per unique person in the ContactPerson list.
            7. Assign emails, phones, addresses, and roles correctly to the associated person or to the general agency lists if no person is indicated.
            8. For Socials, include URLs from facebook.com, x.com, twitter.com, instagram.com, or linkedin.com only; ignore all others.
            9. Output ONLY a single JSON object (target schema) that exactly matches the target schema with no extra text or explanation.
            """
        }

        return self._process_openai_response(agency_name, agency_url, [system_message, user_message])

    def add_facebook_contact_info(self, agency_name: str, agency_url: str, raw_items: List[Dict[str, Any]], contact_detail: ContactDetail) -> Optional[ContactDetail]:
        if not raw_items:
            return contact_detail

        raw_items_json = json.dumps(raw_items, indent=2)
        current_detail = json.dumps(asdict(contact_detail), indent=2)

        system_message = {
            "role": "system",
            "content": "You are a strict JSON data merger and cleaner. Follow the instructions exactly and output only a valid JSON object matching the target schema."
        }

        user_message = {
            "role": "user",
            "content": f"""You are provided with a raw JSON list of contact information extracted from Facebook and the current contact details for "{agency_name}" ({agency_url}). 
            Your task is to update the current contact details by merging in the new Facebook data while cleaning, validating, deduplicating and structure this information into a single final JSON object that EXACTLY matches the target schema below.
            
            Target Schema:
            ```json
            {self.target_schema}
            ```

            Raw JSON List from Facebook:
            ```json
            {raw_items_json}
            ```

            Current Contact Detail:
            ```json
            {current_detail}
            ```

            Rules for Updating Contact Detail:
            1. Use the current contact details as the starting point.
            2. Aggregate the new Facebook data with the existing details and remove exact duplicates (both at the agency level and within each person’s data).
            3. Validate emails by including only those in a correct format.
            4. Validate phone numbers by ensuring they contain only digits; convert valid numbers to integers and discard any invalid entries.
            5. Validate and format addresses:
                - Identify valid mailing (PO Box) or physical (street) addresses.
                - Discard entries that are purely numeric or are URLs.
                - Combine split address fragments into a single complete address. (e.g., "P.O. Box 432" and "Brewton, AL 36427" should be combined into "P.O. Box 432, Brewton, AL 36427").
                - Normalize addresses by removing extra whitespace and standardizing common abbreviations using standard postal formats. (e.g., St to Street, Ave./Ave to Avenue) using standard postal formats.
                - If multiple normalized addresses refer to the same location, keep only the most complete version.
            6. Never remove valid existing data.
            7. Add only new and unique Facebook data that does not duplicate what already exists.
            8. Output ONLY a single JSON object that exactly matches the target schema with no additional text or explanation.
            """
        }

        return self._process_openai_response(agency_name, agency_url, [system_message, user_message])

    def _process_openai_response(self, agency_name: str, agency_url: str, message: list[dict[str, str]]) -> Optional[ContactDetail]:
        try:
            completion = self.client.chat.completions.create(
                model="gpt-4o-mini", # Or a more powerful model if needed for complexity
                messages=message,
                temperature=0,
                stop=["\n\n"],
                response_format={"type": "json_object"}
            )

            response_content = completion.choices[0].message.content.strip()
            if not response_content:
                print("⚠️ OpenAI cleaning step returned an empty response.")
                return None

            cleaned_data = json.loads(response_content)

            information_pending_flag = True
            # Check agency-level lists
            if cleaned_data.get('Email') and cleaned_data.get('Phone') and cleaned_data.get('Address'):
                information_pending_flag = False

            contact_person_data = cleaned_data.get('ContactPerson', [])
            reconstructed_persons = []

            for person_data in contact_person_data:
                mailing_address = []
                physical_address = []
                for address in person_data.get('Address', []):
                    if ('pobox' in re.sub(r'[^a-zA-Z0-9]', '', address).lower()):
                        mailing_address.append(address)
                    else:
                        physical_address.append(address)
                contact_person = ContactPerson(
                    ContactName=person_data.get('ContactName', ''),
                    ContactRole=person_data.get('ContactRole', ''),
                    Email=person_data.get('Email', []),
                    Phone=person_data.get('Phone', []),
                    MailingAddress=mailing_address,
                    PhysicalAddress=physical_address,
                    Socials=[social for social in person_data.get('Socials', []) if any(keyword in social.lower() for keyword in self.social_keywords)]
                )
                reconstructed_persons.append(contact_person)

            mailing_address = []
            physical_address = []
            for address in cleaned_data.get('Address', []):
                if 'pobox' in re.sub(r'[^a-zA-Z0-9]', '', address).lower():
                    mailing_address.append(address)
                else:
                    physical_address.append(address)

            agency_contact = ContactDetail(
                AgencyName=cleaned_data.get('AgencyName', agency_name), # Use provided name as fallback
                AgencyUrl=cleaned_data.get('AgencyUrl', agency_url), # Use provided URL as fallback
                Email=cleaned_data.get('Email', []),
                Phone=cleaned_data.get('Phone', []),
                MailingAddress=mailing_address,
                PhysicalAddress=physical_address,
                ContactPerson=reconstructed_persons,
                Socials=[social for social in cleaned_data.get('Socials', []) if any(keyword in social.lower() for keyword in self.social_keywords)],
                InformationPending=information_pending_flag # Use calculated flag
            )

            return agency_contact

        except json.JSONDecodeError as json_err:
            print(f"❌ Error decoding JSON response from OpenAI: {json_err}")
            print(f"Raw response content:\n{response_content}")
            return None
        except ValidationError as pydantic_err:
            print(f"❌ Pydantic validation error during object reconstruction: {pydantic_err}")
            print(f"Cleaned data that failed validation:\n{cleaned_data}")
            return None
        except Exception as e:
            print(f"❌ An unexpected error occurred during OpenAI cleaning: {e}")
            return None
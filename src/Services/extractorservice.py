from src.services.openaiservice import OpenAIService
from src.services.playwrightcontrol import PlaywrightControl
from src.models.contactmodels import ContactDetail
from src.models.laamodels import LAAgency
from src.mongocontext.mongocontext import MongoContext
from dataclasses import asdict
from typing import Optional


class ExtractContactDetails:
    def __init__(self) -> None:
        self.playwright_control = PlaywrightControl()
        self.openai_service = OpenAIService()
        self.mongo_context = MongoContext()

    def extract_contact_detail(self, laa: LAAgency) -> Optional[ContactDetail]:
        extracted_items = self.extract_html(laa)
        if not extracted_items:
            return None
        contact_detail = self.process_contact_info(laa, extracted_items)
        contact_detail = self.update_from_facebook(laa, contact_detail)
        return contact_detail

    def extract_html(self, laa: LAAgency) -> list[dict[str, str]]:
        html_texts = self.playwright_control.fetch(laa.Website)
        training_data = []
        all_extracted_items = []
        for html_text in html_texts:
            extracted_items = self.openai_service.fetch_contact_info(laa.LAAName, laa.Website, html_text)
            training_data.append({
                "agency_id": laa._id,
                "html_text": html_text,
                "response": extracted_items if extracted_items or any(extracted_items) else None
            })
            if extracted_items:
                all_extracted_items.extend(extracted_items)
        self.save_training_data(laa._id, "html-extraction-data", training_data)
        return all_extracted_items

    def process_contact_info(self, laa:LAAgency, extracted_items: list[dict[str, str]]):
        training_data = []
        contact_detail = self.openai_service.clean_and_structure_contact_info(
            agency_name=laa.LAAName,
            agency_url=laa.Website,
            raw_items=extracted_items
        )
        training_data.append({
            "agency_id": laa._id,
            "extracted_items": extracted_items,
            "response": asdict(contact_detail) if contact_detail else None
        })
        self.save_training_data(laa._id, "contact-detail-data", training_data)
        return contact_detail

    def update_from_facebook(self, laa: LAAgency, contact_detail: ContactDetail):
        facebook_url = next(url for url in contact_detail.Socials if 'facebook.com' in url)
        if not facebook_url or facebook_url == laa.Website:
            return contact_detail
        extracted_items = self.extract_html(laa)
        updated_contact_detail = self.openai_service.add_facebook_contact_info(
            agency_name=laa.LAAName,
            agency_url=laa.Website,
            raw_items=extracted_items,
            contact_detail=contact_detail
        )
        training_data = {
            "agency_id": laa._id,
            "facebook_items": extracted_items,
            "contact_detail": asdict(contact_detail),
            "response": asdict(updated_contact_detail) if contact_detail else None
        }
        self.save_training_data(laa._id, "update-contact-data", training_data)
        return updated_contact_detail


    def save_training_data(self, laaid, collection_name: str, training_data):
        collection = self.mongo_context.get_collection(collection_name)
        if collection.find({'agency_id': laaid}):
            collection.delete_many({'agency_id': laaid})
        collection.insert_many(training_data)

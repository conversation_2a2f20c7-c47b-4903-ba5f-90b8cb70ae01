from typing import Optional, Annotated
from pydantic import BaseModel, Field, field_validator
from beanie import Document, Indexed
from uuid import UUID, uuid4
from src.common.helper import check_if_bool


class ConnectorConfig(BaseModel):
    kafka_brokers: str
    name: Annotated[str, Indexed()]
    topic: str
    platform_type: str
    connector_type: str
    pipeline_id: Annotated[str, Indexed()]
    platform_instance: Optional[str] = None
    database_name: Optional[str] = None
    client_id: Optional[str] = None
    client_secret: Optional[str] = None
    username: Optional[str] = None
    password: Optional[str] = None
    db_encryption: Optional[str] = None
    access_token: Optional[str] = None
    token_expiry: Optional[int] = 0
    refresh_token: Optional[str] = None
    external_id_field: Optional[str] = None
    sf_object_map: Optional[str] = None
    source_platform: Optional[str] = None
    incremental_key: Optional[str] = None
    auto_start: Optional[str] = "true"
    auto_restart: Optional[str] = "true"
    mappings: Optional[str] = None
    topic_mappings: Optional[str] = None
    ignore_fields: Optional[str] = None
    include_fields: Optional[str] = None
    channel: Optional[str] = None
    is_sandbox: Optional[bool] = False
    poll_interval: Optional[int] = None
    ext_connector_config: Optional[dict] = None

    # @field_validator("platform_type")
    # def check_platform_type(cls, _platform_type: str):
    #     assert _platform_type in SUPPORTED_PLATFORMS
    #     return _platform_type.lower()

    @field_validator("connector_type")
    def check_connector_type(cls, _connector_type: str):
        assert _connector_type is not None
        return _connector_type.lower()

    @field_validator("name")
    def check_connector_name(cls, _name: str):
        assert _name is not None
        return _name.lower()

    @field_validator("auto_start")
    def check_auto_start(cls, _auto_start: Optional[str] = None):
        if _auto_start is None:
            return "true"
        return check_if_bool(_auto_start)

    @field_validator("auto_restart")
    def check_auto_restart(cls, _auto_restart: Optional[str] = None):
        if _auto_restart is None:
            return "true"
        return check_if_bool(_auto_restart)


class ConnectorInfo(Document):
    name: Annotated[str, Indexed(unique=True)]
    group: str
    start: str
    stop: str
    now: str
    state: int
    statename: str
    spawnerr: Optional[str] = None
    exitstatus: int = 0
    logfile: str
    stdout_logfile: str
    stderr_logfile: str
    pid: int
    description: str
    config: Optional[ConnectorConfig] = None
    id: UUID = Field(default_factory=uuid4)

    @field_validator('start', 'stop', 'now')
    def check_times(cls, val: str):
        if not val or val == '':
            return "N/A"
        return val

    class Settings:
        name: str = "managedconnectors"
        keep_nulls = False

from typing import Optional, List, Any
from pydantic.dataclasses import dataclass
from pydantic import HttpUrl, EmailStr, ConfigDict, field_validator, SkipValidation
from bson import ObjectId
from datetime import datetime, timezone, UTC
import uuid
from dataclasses import field
from pydantic_mongo import PydanticObjectId
from dataclasses import asdict


@dataclass
class BaseContact:
    Email: List[EmailStr] = field(default_factory=list)
    Phone: List[int] = field(default_factory=list)
    MailingAddress: List[str] = field(default_factory=list)
    PhysicalAddress: List[str] = field(default_factory=list)
    Socials: List[str] = field(default_factory=list)

@dataclass(kw_only=True)
class ContactPerson(BaseContact):
    ContactName: str
    ContactRole: Optional[str] = None

@dataclass(config=ConfigDict(
    arbitrary_types_allowed=True,
    json_encoders={ObjectId: str,
                   ContactPerson: lambda v: asdict(v)
    },
), kw_only=True)
class ContactDetail(BaseContact):
    AgencyName: str
    AgencyUrl: str
    InformationPending: bool = True
    ContactPerson: List[ContactPerson] = field(default_factory=list)
    SystemUpdated: bool = True
    DateCreated: datetime = field(default_factory=lambda: datetime.now(UTC))
    DateModified: datetime = field(default_factory=lambda: datetime.now(UTC))
    Jurisdiction: Optional[str] = None
    OrganizationType: Optional[str] = None
    ETag: Optional[str] = None
    LAAId: PydanticObjectId = field(default_factory=PydanticObjectId)
    _id: PydanticObjectId = field(default_factory=PydanticObjectId)

    def generate_etag(self) -> None:
        self.ETag = str(uuid.uuid4())

    @field_validator('ETag', mode='before')
    @classmethod
    def validate_etag(cls, v: Any) -> str | None:
        if v is None:
            return None
        return str(v)

    @field_validator('Jurisdiction', mode='before')
    @classmethod
    def normalize_jurisdiction(cls, v: str) -> str:
        if v is None:
            return 'Other'
        if v.lower() == 'city':
            return 'City'
        elif v.lower == "other":
            return 'Other'
        else:
            return 'County / Regional'

    @field_validator('OrganizationType', mode='before')
    @classmethod
    def normalize_organization_type(cls, v: str) -> str:
        if v is None:
            return 'Other'
        if v.lower() == 'government' or v.lower() == 'gov':
            return 'Government'
        elif v.lower == "non-profit" or v.lower().strip(' ') == 'nonprofit':
            return 'Non-profit'
        else:
            return 'Other'

    @field_validator('Email', mode='before')
    @classmethod
    def normalize_emails(cls, v: List[Any]) -> List[EmailStr]:
        if not v:
            return []
        return [str(email).lower() for email in v]

    @field_validator('MailingAddress', mode='before')
    @classmethod
    def normalize_mailing_address(cls, v: List[Any]) -> List[str]:
        if not v:
            return []
        return [str(address).strip() for address in v]

    @field_validator('PhysicalAddress', mode='before')
    @classmethod
    def normalize_physical_address(cls, v: List[Any]) -> List[str]:
        if not v:
            return []
        return [str(address).strip() for address in v]

    @field_validator('Socials', mode='before')
    @classmethod
    def normalize_socials(cls, v: List[Any]) -> List[str]:
        if not v:
            return []
        return [str(social).lower() for social in v]

from dataclasses import field
from datetime import datetime
from pydantic.dataclasses import dataclass
from pydantic import HttpUrl, ConfigDict, field_validator
from typing import Optional, Any
from bson import ObjectId
import uuid
from pydantic_mongo import PydanticObjectId


@dataclass(config=ConfigDict(
    arbitrary_types_allowed=True,
    json_encoders={ObjectId: str}
))
class LAAgency:
    LAAName: str
    Website: Optional[str] = None
    DateCreated: datetime = field(default_factory=datetime.now)
    DateModified: datetime = field(default_factory=datetime.now)
    ETag: Optional[str] = None
    _id: PydanticObjectId = field(default_factory=PydanticObjectId)

    def generate_etag(self) -> None:
        self.ETag = str(uuid.uuid4())

    @field_validator('Website', mode='before')
    @classmethod
    def validate_website(cls, v: Any) -> str | None:
        if v is None:
            return None
        if isinstance(v, str):
            v = v.lower().strip()
            if not v.startswith(('http://', 'https://')):
                if v.startswith('www.'):
                    v = f'https://{v}'
                else:
                    v = f'http://www.{v}'
            # Validate that it's a proper URL
            try:
                HttpUrl(v)
            except ValueError as e:
                return None
            return v
        raise ValueError("Website must be a string")

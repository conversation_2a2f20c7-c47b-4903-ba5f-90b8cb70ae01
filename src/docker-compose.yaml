services:
  aftaws-api:
    image: aftaws-api:1.0.0_py3.12.9
    container_name: aftaws-api
    ports:
      - "5000:5000"
      - "5080:5080"
    environment:
      - CONTACT_COLLECTION=ContactCollection
      - DB_NAME=laadb
      - LAA_COLLECTION=LAACollection
      - MONGO_CONNECTION=mongodb://*************:27017/
      - APIFY_API_TOKEN=**********************************************
      - OPENAI_API_KEY=********************************************************************************************************************************************************************
      - CONTACT_REFRESH_DAYS=365
    healthcheck:
      test: [
        'CMD',
        '--silent',
        '--fail',
        '-X',
        'GET',
        'http://localhost:5000/health'
      ]
      start_period: 30s
      interval: 10s
      timeout: 5s
      retries: 5

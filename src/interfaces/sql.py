from src.interfaces.mssqlserver import MSSQLServerClient
from src.interfaces.postgresql import PostgreSQLClient
from src.models.platformmodels import PlatformConfig


def get_sql_client(plt_config: PlatformConfig):
    if "mssqlserver" in plt_config.name:
        return MSSQLServerClient(plt_config)
    elif "postgres" in plt_config.name:
        return PostgreSQLClient(plt_config)
    raise NotImplementedError

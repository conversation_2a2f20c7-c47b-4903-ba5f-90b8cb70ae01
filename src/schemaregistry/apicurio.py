import json
from src.common.requesthelper import session
from typing import Optional


class RegistryClient:
    def __init__(self, registry_url: str) -> None:
        self.registry_url = f"{registry_url}/apis/registry/v2/"

    def create_headers(self, artifact_id: str, artifact_name: str, artifact_version: str) -> dict:
        return {
            "Content-Type": "application/json; artifactType=JSON",
            "X-Registry-ArtifactId": artifact_id,
            "X-Registry-Version": artifact_version,
            "X-Registry-ArtifactType": "JSON",
            "X-Registry-Name": artifact_name
        }

    def schema_exists(self, group_id: str, artifact_id: str) -> bool:
        artifact_url = f"{self.registry_url}groups/{group_id}/artifacts/"
        response = session.get(f"{artifact_url}{artifact_id}")
        if response.status_code == 404:
            return False
        return True

    def schema_version_exists(self, group_id: str, artifact_id: str, version: str) -> bool:
        artifact_url = f"{self.registry_url}groups/{group_id}/artifacts/"
        version_url = f"{artifact_url}{artifact_id}/versions/"
        response = session.get(version_url)
        if response.status_code == 404:
            return False
        versions = [v['version'] for v in response.json()['versions']]
        if version in versions:
            return True
        return False

    def create_schema(self, schema: dict, group_id: str, artifact_id: str, artifact_name: str, artifact_version: str) \
            -> dict:
        headers = self.create_headers(artifact_id, artifact_name, artifact_version)
        artifact_url = f"{self.registry_url}groups/{group_id}/artifacts/"
        response = session.post(artifact_url, data=json.dumps(schema), headers=headers)
        if response.status_code != 200:
            raise Exception("Something went wrong when registering new schema. Please check connection.")
        return response.json()

    def update_schema(self, schema: dict, group_id: str, artifact_id: str, artifact_name: str, artifact_version: str) \
            -> dict:
        headers = self.create_headers(artifact_id, artifact_name, artifact_version)
        artifact_url = f"{self.registry_url}groups/{group_id}/artifacts/"
        schema_url = f"{artifact_url}{schema['name']}"
        response = session.put(schema_url, data=json.dumps(schema), headers=headers)
        if response.status_code != 200:
            raise Exception("Something went wrong when updating schema. Please check connection.")
        return response.json()

    def get_schema(self, group_id: str, artifact_id: str, artifact_version: Optional[str] = None) -> dict:
        schema_url = f"{self.registry_url}groups/{group_id}/artifacts/{artifact_id}"
        if artifact_version:
            schema_url = f"{schema_url}/versions/{artifact_version}"
        response = session.get(schema_url)
        if response.status_code != 200:
            raise Exception("Something went wrong while fetching schema. Please check connection.")
        return response.json()

    def get_schema_meta(self, group_id: str, artifact_id: str) -> dict:
        artifact_url = f"{self.registry_url}groups/{group_id}/artifacts/"
        schema_meta_url = f"{artifact_url}{artifact_id}/meta"
        response = session.get(schema_meta_url)
        if response.status_code != 200:
            raise Exception("Something went wrong while fetching schema. Please check connection.")
        return response.json()

    def get_schema_by_globalid(self, schema_id: int) -> dict:
        artifact_url = f"{self.registry_url}ids/globalIds/{schema_id}"
        response = session.get(artifact_url)
        if response.status_code != 200:
            raise Exception("Something went wrong while fetching schema. Please check connection.")
        return response.json()

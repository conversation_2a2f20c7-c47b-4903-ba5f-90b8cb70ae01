[supervisord]
nodaemon=true
logfile=/dev/null
logfile_maxbytes=1024
logfile_backups=0
loglevel=error

[rpcinterface:supervisor]
supervisor.rpcinterface_factory = supervisor.rpcinterface:make_main_rpcinterface

[rpcinterface:connector]
supervisor.rpcinterface_factory = src.worker.rpcinterface:make_connector_rpcinterface

[program:pyconnectors-api]
command=uvicorn main:app --host * --port 3000
autostart=true
autorestart=true
stopsignal=INT
stopwaitsecs=10
stderr_logfile=/var/log/uvicorn.err.log
stdout_logfile=/var/log/uvicorn.out.log

[inet_http_server]
port=*:3080

[group:connectors]
priority=900

[group:test]
priority=999
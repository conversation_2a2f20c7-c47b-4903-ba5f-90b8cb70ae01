[supervisord]
nodaemon=true
logfile=/dev/null
logfile_maxbytes=1024
logfile_backups=0
loglevel=error

[rpcinterface:supervisor]
supervisor.rpcinterface_factory = supervisor.rpcinterface:make_main_rpcinterface

[program:aftaws-api]
command=uvicorn main:app --host * --port 5000
autostart=true
autorestart=true
stopsignal=INT
stopwaitsecs=10
stderr_logfile=/var/log/uvicorn.err.log
stdout_logfile=/var/log/uvicorn.out.log

[inet_http_server]
port=*:5080

[group:test]
priority=999
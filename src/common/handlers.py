from datetime import datetime,date, time, timedelta
import uuid
from bson import ObjectId
from pydantic_mongo import PydanticObjectId
from deepdiff import DeepDiff


def pydantic_json_encoder(obj):
    if isinstance(obj, datetime):
        return obj.isoformat()
    elif isinstance(obj, date):
        return obj.isoformat()
    elif isinstance(obj, time):
        return obj.isoformat()
    elif isinstance(obj, timedelta):
        return obj.total_seconds()
    elif isinstance(obj, uuid.UUID):
        return str(obj)
    elif isinstance(obj, ObjectId):
        return str(obj)
    elif isinstance(obj, PydanticObjectId):
        return str(obj)
    return TypeError(f"Object of type {obj.__class__.__name__} is not JSON serializable. Update pydantic_json_encoder() to handle this type.")


def get_changed_fields(t1, t2=None, ignore_order=True, ignore_fields: list[str] = None, include_fields: list[str] = None) -> list[str]:
    changed_fields = []
    if not t2:
        changed_fields = [key for key in t1 if t1[key] is not None]
    else:
        ddiff = DeepDiff(t1, t2, ignore_order=ignore_order)
        changed_fields = list(ddiff.affected_root_keys)
    if ignore_fields:
        changed_fields =  [key for key in changed_fields if key not in ignore_fields]
    if include_fields:
        changed_fields =  [key for key in changed_fields if key in include_fields]
    return changed_fields

import requests


class RequestHandler(requests.Session):
    def __init__(self):
        super().__init__()

    def request(self, method, url, *args, **kwargs):
        response = super().request(method, url, *args, **kwargs)
        if response.history and response.history[0].status_code == 302:
            response = super().request(method, response.url, *args, **kwargs)
        return response


session = RequestHandler()

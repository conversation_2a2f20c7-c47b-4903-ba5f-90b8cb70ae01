from fastapi import <PERSON><PERSON><PERSON>, HTTPException
from fastapi.responses import J<PERSON><PERSON>esponse
from src.services.servicesextension import *
from src.services.middlewares import *
from starlette.middleware.base import BaseHTTPMiddleware
from src.services.servicemanager import ServiceRepository
from contextlib import asynccontextmanager
import uvicorn
import os


logger.info(os.environ.__dict__)
logger.info('Starting API...')


@asynccontextmanager
async def lifespan(api_app: FastAPI):
    try:
        db_context = DbContext()
        await db_context.initialize_client()
        service = ServiceRepository(db_context)
        await service.on_startup()
        logger.info('Startup completed...')
        yield
        db_context.close()
        logger.info('Shutting down...')
    except Exception as e:
        exp_str = "Exception in lifespan: "+str(e)
        logger.error(exp_str)

if DEBUG_MODE:
    app = FastAPI()
else:
    app = FastAPI(lifespan=lifespan)

# app.add_middleware(BaseHTTPMiddleware, dispatch=log_middleware)


@app.get("/")
async def root():
    return {"message": "Uvicorn app is running, see http://localhost:3080 for supervisor U<PERSON>"}


@app.get("/platforms")
async def get_platforms(service: ServiceRepository = Depends(get_service_repository)):
    result = await service.get_supported_platforms()
    if result is None:
        raise HTTPException(status_code=404, detail="Platforms not found.")
    return result


@app.post("/platforms")
async def add_platform(config: PlatformConfig, service: ServiceRepository = Depends(get_service_repository)):
    return await service.add_platform(config)


@app.get("/connectors")
async def get_connectors(service: ServiceRepository = Depends(get_service_repository)):
    result = await service.get_connectors()
    if result is None:
        raise HTTPException(status_code=404, detail="Connectors not found.")
    return result


@app.get("/connectors/{name}/status")
async def get_connector(name: str, service: ServiceRepository = Depends(get_service_repository)):
    result = await service.get_connector(name)
    if result is None:
        raise HTTPException(status_code=404, detail=f"Connector {name} not found.")
    return result


@app.get("/connectors/{name}/start")
async def start_connector(name: str, service: ServiceRepository = Depends(get_service_repository)):
    return await service.start_connector(name)


@app.get("/connectors/{name}/stop")
async def stop_connector(name: str, service: ServiceRepository = Depends(get_service_repository)):
    return await service.stop_connector(name)


@app.post("/connectors")
async def create_connector(config: ConnectorConfig, service: ServiceRepository = Depends(get_service_repository)):
    return await service.create_connector(config)


@app.put("/connectors")
async def update_connector(config: ConnectorConfig, service: ServiceRepository = Depends(get_service_repository)):
    return await service.update_connector(config)


@app.delete("/connectors/{name}")
async def delete_connector(name: str, service: ServiceRepository = Depends(get_service_repository)):
    await service.delete_connector(name)
    return JSONResponse(status_code=204, content={})


if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=5000)

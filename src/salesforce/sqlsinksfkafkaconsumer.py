import asyncio
import signal
import json
import sys
from typing import <PERSON><PERSON>, Dict, Any, List

from src.interfaces.sql import get_sql_client
from src.models.sqlmodels import create_sql_field
from src.salesforce.salesforce import *
from src.common.helper import *
from kafka import <PERSON>fkaConsumer
from src.salesforce.sfschemamanager import SchemaManager
from src.salesforce.sftransformer import convert_to_sql_schema
from src.salesforce.sqlhandler import create_sql_schema


config = ConnectorConfig(**get_arguments(ConnectorConfig))
logger.info(config)


def exit_handler(signum, frame):
    sql_client.connection.close()
    logger.info(f"SQL Connection Closed. Shutting down connector: {config.name}")
    sys.exit(0)


signal.signal(signal.SIGINT, exit_handler)


async def get_platform_config(platform_type: str) -> PlatformConfig:
    db_context = DbContext()
    await db_context.initialize_client()
    platform_config = await db_context.get_managed_platform(platform_type)
    db_context.close()
    return platform_config


loop = asyncio.get_event_loop()
sf_plt_config = loop.run_until_complete(get_platform_config("salesforce"))
sf_client = SalesforceClient(sf_plt_config)
plt_config = loop.run_until_complete(get_platform_config(config.platform_type))
sql_client = get_sql_client(plt_config)
try:
    sql_client.authenticate_connector(config)
    sql_client.metadata.reflect(sql_client.engine)
    sql_client.connection = sql_client.engine.connect()
except Exception as e:
    logger.critical(e, exc_info=True)
    raise e

schema_manager = SchemaManager()
consumer = KafkaConsumer(config.topic,
                         auto_offset_reset='earliest',
                         enable_auto_commit=False,
                         group_id=config.pipeline_id,
                         bootstrap_servers=KAFKA_BROKER,
                         value_deserializer=lambda msg: json.loads(msg.decode('utf-8')),
                         key_deserializer=lambda k: k.decode('utf-8') if k is not None else None
                        )


def register_schema(sfobject: str, schema_uuid: str) -> tuple[dict, dict]:
    if schema_manager.schema_exists(config.pipeline_id, f"{sfobject}.SQL", schema_uuid):
        sql_schema = schema_manager.get_schema(config.pipeline_id, f"{sfobject}.SQL", schema_uuid)
        field_description = schema_manager.get_field_description(config.pipeline_id, sfobject, schema_uuid)
        return sql_schema, field_description
    source_schema = schema_manager.get_schema(config.pipeline_id, sfobject, schema_uuid)
    field_description = schema_manager.get_field_description(config.pipeline_id, sobject, schema_uuid)
    sql_schema = {
        "source": "salesforce",
        "name": source_schema["name"],
        "type": "record",
        "uuid": schema_uuid,
        "fields": create_sql_schema(config.platform_type, source_schema, config.external_id_field, field_description)
    }
    schema_manager.register_schema(config.pipeline_id, f"{sfobject}.SQL", sql_schema, schema_uuid)
    return sql_schema, field_description

def get_schema(schema_id: int) -> tuple[dict[str, str | list[dict] | int | Any], dict]:
    field_description = schema_manager.get_schema_by_id(schema_id)
    if field_description:
        sql_schema = {
        "source": "salesforce",
        "name": field_description["name"],
        "type": "record",
        "uuid": schema_id,
        "fields": create_sql_schema(config.platform_type, None, config.external_id_field, field_description)
        }
    return sql_schema, field_description

def create_table(table: str, sql_schema: list[SQLField]) -> bool:
    try:
        sql_client.create_table(table, sql_schema)
        sql_client.metadata.reflect(sql_client.engine)
        return True
    except Exception as err:
        logger.critical(err, exc_info=True)
        raise err


while True:
    topic_map_dict = None
    if config.topic_mappings:
        topic_map_dict = dict(pair.split(':') for pair in config.topic_mappings.split(','))

    for message in consumer:
        logger.info(message)
        try:
            msg_payload_keys = message.value['payload'].keys()
            if "ChangeEventHeader" in msg_payload_keys:
                sobject = message.value['payload']['ChangeEventHeader']['entityName']
                operation = message.value['payload']['ChangeEventHeader']['changeType']
                schema, field_desc = register_schema(sobject, message.key)
            else:
                sobject = None
                operation = "BULK"
                schema, field_desc = get_schema(message.value["schema"])

            if topic_map_dict and config.topic in topic_map_dict.keys():
                table_name = topic_map_dict[config.topic]
            else:
                table_name = f"{config.topic}"

            ignore_fields = []
            if config.ignore_fields:
                ignore_fields.extend(config.ignore_fields.split(','))
            if config.include_fields:
                include_fields = config.include_fields.split(',')
                sql_fields = [create_sql_field(field, config.external_id_field) for field in schema["fields"]
                              if field['name'] in include_fields
                            and field['name'] not in ignore_fields]
            else:
                sql_fields = [create_sql_field(field, config.external_id_field) for field in schema["fields"]]
            payload = convert_to_sql_schema(message.value, config.external_id_field, sql_fields, field_desc)

            if config.mappings:
                map_dict = dict(pair.split(':') for pair in config.mappings.split(','))
                
                record = {map_dict[sql_field.name]: payload[sql_field.name] for sql_field in sql_fields if sql_field.name in payload}
                logger.info(record)
                pk = map_dict[config.external_id_field]
            else:
                pk = config.external_id_field
                record = {sql_field.name: payload[sql_field.name] for sql_field in sql_fields if sql_field.name in payload}

            if not sql_client.has_table(table_name):
                has_table = create_table(table_name, sql_fields)
            # payload = convert_to_sql_schema(message.value, config.external_id_field, sql_fields, field_desc)
            try:
                sql_client.handle_message(config,
                                        sql_fields=sql_fields,
                                        payload=record,
                                        operation=operation,
                                        table_name=table_name,
                                        pk=pk)
                consumer.commit()
            except Exception as error:
                logger.critical(error, exc_info=True)
                raise error
        except Exception as e:
            exp_msg = "Exption in handling salesforce src msg: "+str(e)
            logger.error(exp_msg)
            logger.critical(e, exc_info=True)

import asyncio
import json
from src.salesforce.salesforce import *
from src.common.helper import *
from kafka import <PERSON>fkaConsumer
from src.salesforce.sfschemamanager import SchemaManager

config = ConnectorConfig(**get_arguments(ConnectorConfig))
logger.info(config)
schema_manager = SchemaManager()


async def get_platform_config():
    db_context = DbContext()
    await db_context.initialize_client()
    platform_config = await db_context.get_managed_platform(config.platform_type)
    db_context.close()
    return platform_config

loop = asyncio.get_event_loop()
plt_config = loop.run_until_complete(get_platform_config())
client = SalesforceClient(plt_config)
consumer = KafkaConsumer(config.topic,
                         auto_offset_reset='earliest',
                         enable_auto_commit=False,
                         group_id=config.pipeline_id,
                         bootstrap_servers=KAFKA_BROKER,
                         # value_deserializer=lambda msg: json.loads(msg.decode('ascii')),
                         # key_deserializer=lambda msg: json.loads(msg.decode('ascii')))
                         value_deserializer=lambda msg: json.loads(msg.decode('utf-8')) if msg is not None else None,
                         key_deserializer=lambda msg: json.loads(msg.decode('utf-8')))


def get_schema(schema_id: int) -> dict:
    return schema_manager.get_schema_by_id(schema_id)


def get_primary_field(schema: dict):
    return schema["fields"][0]["field"]


async def consume():
    for message in consumer:
        logger.info(message)
        key_schema = get_schema(message.key["schemaId"])
        primary_key = get_primary_field(key_schema)
        is_success = await client.handle_sql_message(config, message=message, primary_key=primary_key)
        if is_success:
            consumer.commit()

while True:
    loop.run_until_complete(consume())

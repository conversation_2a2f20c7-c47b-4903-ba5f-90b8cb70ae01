from fastapi import APIRouter, HTTPException, status, Response, BackgroundTasks
from typing import List, Optional
from fastapi.responses import JSONResponse
from bson import ObjectId
import json
from starlette.responses import FileResponse
from src.api.laaendpoints import laa_repository
from src.common.handlers import pydantic_json_encoder
from src.repositories.contactrepository import ContactRepository, ContactRepositoryError
from src.services.backgroundservice import BackgroundTaskService
from src.models.contactmodels import ContactDetail
from src.services.excelwriter import create_excel_from_json
from dataclasses import asdict
import os
from multiprocessing import Pool, cpu_count


router = APIRouter(
    prefix="/contact",
    tags=["Contact"],
    responses={404: {"description": "Not found"}}
)

contact_repository = ContactRepository()
bg_service = BackgroundTaskService()

@router.get("/scrape", status_code=status.HTTP_200_OK)
async def scrape_contact(bg_tasks: BackgroundTasks, id: Optional[str] = None, url: Optional[str] = None, force: bool = False):
    try:
        # Check if there's already a task running for this ID or URL
        metadata = {}
        if id:
            metadata["laa_id"] = ObjectId(id)
            # Check for existing in-progress or queued task
            existing_tasks = bg_service.get_tasks_by_metadata("laa_id", ObjectId(id), status=["queued", "in_progress"])
            if existing_tasks:
                return JSONResponse(
                    content=json.loads(json.dumps(existing_tasks[0], default=pydantic_json_encoder)),
                    status_code=status.HTTP_202_ACCEPTED
                )
        if url:
            norm_url = url.lower()
            metadata["laa_url"] = norm_url
            # Check for existing in-progress or queued task
            existing_tasks = bg_service.get_tasks_by_metadata("laa_url", norm_url, status=["queued", "in_progress"])
            if existing_tasks:
                return JSONResponse(
                    content=json.loads(json.dumps(existing_tasks[0], default=pydantic_json_encoder)),
                    status_code=status.HTTP_202_ACCEPTED
                )

        def scrape_task():
            laa_id = ObjectId(id) if id else None
            return contact_repository.scrape(laa_id, url, force)

        task_id = bg_service.add_background_task(
            background_tasks=bg_tasks,
            func=scrape_task
        )

        bg_service.update_task(
            task_id,
            metadata=metadata
        )

        task = bg_service.get_task(task_id)
        return JSONResponse(content=json.loads(json.dumps(task, default=pydantic_json_encoder)))

    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except ContactRepositoryError as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        )

@router.get("/scrape/all")
async def scrape_all_contacts(bg_tasks: BackgroundTasks, skip: int = 0, limit: int = None, force: bool = True):
    try:
        num_processes = cpu_count() - 1 if cpu_count() <= 6 else cpu_count() - 4
        # num_processes = 1
        def scrape_all_task():
            laas = laa_repository.get_agencies(skip, limit)
            print(f"Scraping {len(laas)} agencies using {num_processes} processes")
            tasks = [(laa._id, force) for laa in laas]

            with Pool(processes=num_processes) as pool:
                results = pool.starmap(_scrape_worker, tasks)

            message = f"Processed {len(results)} agencies using {num_processes} processes"
            print(message)
            return message

        task_id = bg_service.add_background_task(
            background_tasks=bg_tasks,
            func=scrape_all_task
        )

        bg_service.update_task(
            task_id,
            metadata={"type": "scrape_all", "processes": num_processes}
        )

        task = bg_service.get_task(task_id)
        return JSONResponse(content=json.loads(json.dumps(task, default=pydantic_json_encoder)))

    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except ContactRepositoryError as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        )

@router.get("", response_model=ContactDetail, status_code=status.HTTP_200_OK)
async def get_contact(id: Optional[str] = None, url: Optional[str] = None):
    if not id and not url:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Either 'id' or 'url' query parameter must be provided"
        )
    try:
        if id:
            contact = contact_repository.get_by_laa(ObjectId(id))
        else:
            contact = contact_repository.get_by_laa(laa_url=url)
        if not contact:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Contact not found"
            )
        return contact
    except ValueError:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid ID format"
        )
    except ContactRepositoryError as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        )

@router.put("/{id}", response_model=ContactDetail, status_code=status.HTTP_200_OK)
async def update_contact(id: str, contact: ContactDetail):
    try:
        if not contact.ETag:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="ETag is required for updates"
            )
        return contact_repository.update_contact(ObjectId(id), contact)
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except ContactRepositoryError as e:
        if "Concurrency conflict" in str(e):
            raise HTTPException(
                status_code=status.HTTP_409_CONFLICT,
                detail=str(e)
            )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        )

@router.get("/export/excel", response_class=FileResponse)
async def export_contact_excel():
    try:
        contacts = contact_repository.get_contacts()
        if not contacts or len(contacts) == 0:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="No contacts found"
            )
        # json_data = json.dumps([asdict(contact) for contact in contacts], default=pydantic_json_encoder, indent=2)
        json_data = [asdict(contact) for contact in contacts]
        print(f"Exporting {len(contacts)} contacts to Excel")
        print(f"Current Running Directory: {os.getcwd()}")
        output_file = "exports/art_agencies.xlsx"
        create_excel_from_json(json_data, output_file)
        return output_file
    except ContactRepositoryError as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        )

def _scrape_worker(laa_id, force):
    return contact_repository.scrape(laa_id, force=force)
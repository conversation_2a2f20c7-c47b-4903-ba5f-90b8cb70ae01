import os

MONGO_CONNECTION = os.getenv("MONGO_CONNECTION")
if not MONGO_CONNECTION:
    raise ValueError("MONGO_CONNECTION environment variable is not set")

DB_NAME = os.getenv("DB_NAME")
if not DB_NAME:
    raise ValueError("DB_NAME environment variable is not set")

CONTACT_COLLECTION = os.getenv("CONTACT_COLLECTION")
if not CONTACT_COLLECTION:
    raise ValueError("CONTACT_COLLECTION environment variable is not set")

LAA_COLLECTION = os.getenv("LAA_COLLECTION")
if not LAA_COLLECTION:
    raise ValueError("LAA_COLLECTION environment variable is not set")

OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")
if not OPENAI_API_KEY:
    raise ValueError("OPENAI_API_KEY environment variable is not set")

CONTACT_REFRESH_DAYS = int(os.getenv("CONTACT_REFRESH_DAYS", 365))
if not isinstance(CONTACT_REFRESH_DAYS, int):
    raise ValueError("CONTACT_REFRESH_DAYS environment variable is not a valid integer")
if CONTACT_REFRESH_DAYS < 0:
    raise ValueError("CONTACT_REFRESH_DAYS environment variable must be a positive integer")

APIFY_API_TOKEN = os.getenv("APIFY_API_TOKEN")
if not APIFY_API_TOKEN:
    raise ValueError("APIFY_API_TOKEN environment variable is not set")

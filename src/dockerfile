FROM python:3.12.7-slim-bookworm
WORKDIR /usr/src
COPY . .
RUN apt-get update && apt-get install -y wget gnupg && apt-get clean
RUN pip install --upgrade pip setuptools
RUN pip install --no-cache-dir -r /usr/src/requirements.txt
RUN wget -qO- https://packages.microsoft.com/keys/microsoft.asc | gpg --dearmor > /usr/share/keyrings/microsoft-archive-keyring.gpg
RUN mv microsoft.asc /etc/apt/trusted.gpg.d/microsoft.asc
COPY mssql-release.list /etc/apt/sources.list.d/mssql-release.list
RUN echo "deb [signed-by=/usr/share/keyrings/microsoft-archive-keyring.gpg] https://packages.microsoft.com/debian/12/prod bookworm main" > /etc/apt/sources.list.d/mssql-release.list
RUN apt-get update && apt-get install -y supervisor && apt-get install -y unixodbc
RUN ACCEPT_EULA=Y apt-get install -y msodbcsql18
RUN rm -rf /var/lib/apt/lists/*
RUN pip cache purge
COPY supervisord.conf /etc/supervisor/conf.d/supervisord.conf
EXPOSE 3000 3080
ENV PYTHONPATH="${PYTHONPATH}:/usr"
# CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "3000"]
CMD ["/usr/bin/supervisord", "-c", "/etc/supervisor/conf.d/supervisord.conf"]
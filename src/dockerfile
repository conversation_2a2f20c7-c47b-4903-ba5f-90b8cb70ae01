FROM python:3.12.9-slim-bookworm
WORKDIR /usr/src
COPY . .
RUN apt-get update && apt-get install -y wget gnupg supervisor
R<PERSON> pip install --upgrade pip setuptools
RUN pip install --no-cache-dir -r /usr/src/requirements.txt
RUN rm -rf /var/lib/apt/lists/*
RUN pip cache purge
RUN apt-get upgrade && apt-get clean
COPY supervisord.conf /etc/supervisor/conf.d/supervisord.conf
EXPOSE 5000 5080
ENV PYTHONPATH="${PYTHONPATH}:/usr"
CMD ["/usr/bin/supervisord", "-c", "/etc/supervisor/conf.d/supervisord.conf"]

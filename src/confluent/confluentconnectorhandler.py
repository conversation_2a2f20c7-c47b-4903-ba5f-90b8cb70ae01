import asyncio
import io
import csv
import time
from src.common.helper import *
from src.debezium.debezium import DebeziumClient
from src.salesforce.salesforce import *
import json
from kafka import KafkaConsumer, TopicPartition

# get configuration details to manage a debezium connector

connector_config = get_arguments(ConnectorConfig)
config = ConnectorConfig(**connector_config)
logger.info(config)


# loop = asyncio.get_event_loop()

async def get_platform_config():
    db_context = DbContext()
    await db_context.initialize_client()
    platform_config = await db_context.get_managed_platform(config.platform_type)
    db_context.close()
    return platform_config

plt_config = get_platform_config()
client = DebeziumClient(plt_config)


while True:
    # 1. confluent connector name is in variable: connector_config['debezium_config']['name']
    # 2. start the connector
    # 3. sleep for 'X' minutes (X will be in debezium_config)
    # 4. stop the connector
    # 5. get the latest 'DateModified' field in the topic mentioned in connector_config['debezium_config']['config']
        # the consumer code you wrote to capture latest message
    # 6. update the WHERE clause
        # if f"WHERE {incremental_key}" in custom_query:
            # change value after >= (use regex if needed)
        # else:
            # Add WHERE clause with value (value captured from above step 5)
    # 7. update the connector config (call update_connector(pass new updated connector config)
    # 8. time.sleep(1)
    pass
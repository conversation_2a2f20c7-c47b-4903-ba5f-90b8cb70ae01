import pandas as pd
from models import *

class SalonRepository:
    def __init__(self):
        self.customers = pd.read_csv('data/customers.csv')
        self.appointments = pd.read_csv('data/appointments.csv')
        self.hairdressers = pd.read_csv('data/hairdressers.csv')
        self.treatments = pd.read_csv('data/treatments.csv')
        self.appointment_treatments = pd.read_csv('data/appointment_treatments.csv')

    def get_customer(self, phone: int) -> Customer:
        customer_data = self.customers[self.customers['phone_number'] == phone].iloc[0]
        return Customer(
            customer_id=customer_data['customer_id'],
            name=customer_data['name'],
            gender=customer_data['gender'],
            phone_number=customer_data['phone_number'],
            email=customer_data['email'],
            join_date=customer_data['join_date']
        )
    
    def get_treatments(self) -> list[Treatment]:
        treatments_data = self.treatments
        return [Treatment(
            treatment_id=row['treatment_id'],
            treatment_name=row['treatment_name'],
            duration_minutes=row['duration_minutes'],
            price_aed=row['price_aed']
        ) for _, row in treatments_data.iterrows()]
    
    def get_hairdressers(self) -> list[Hairdresser]:
        hairdressers_data = self.hairdressers
        return [Hairdresser(
            hairdresser_id=row['hairdresser_id'],
            name=row['name'],
            experience_years=row['experience_years'],
            phone_number=row['phone_number'],
            email=row['email']
        ) for _, row in hairdressers_data.iterrows()]
    
    def get_appointments(self, customer_id: str) -> list[Appointment]:
        appointments_data = self.appointments[self.appointments['customer_id'] == customer_id]
        return [Appointment(
            appointment_id=row['appointment_id'],
            appointment_date=row['appointment_date'],
            customer_id=row['customer_id'],
            hairdresser_id=row['hairdresser_id'],
            start_time=row['start_time'],
            end_time=row['end_time'],
            total_price_aed=row['total_price_aed'],
            status=row['status']
        ) for _, row in appointments_data.iterrows()]
    
    def get_last_appointment(self, customer_id: str) -> Appointment:
        appointments_data = self.appointments[self.appointments['customer_id'] == customer_id]
        last_appointment = appointments_data.sort_values(by='appointment_date', ascending=False).iloc[0]
        return Appointment(
            appointment_id=last_appointment['appointment_id'],
            appointment_date=last_appointment['appointment_date'],
            customer_id=last_appointment['customer_id'],
            hairdresser_id=last_appointment['hairdresser_id'],
            start_time=last_appointment['start_time'],
            end_time=last_appointment['end_time'],
            total_price_aed=last_appointment['total_price_aed'],
            status=last_appointment['status']
        )
    
    def get_hairdresser(self, hairdresser_id: int) -> Hairdresser:
        hairdresser_data = self.hairdressers[self.hairdressers['hairdresser_id'] == hairdresser_id].iloc[0]
        return Hairdresser(
            hairdresser_id=hairdresser_data['hairdresser_id'],
            name=hairdresser_data['name'],
            experience_years=hairdresser_data['experience_years'],
            phone_number=hairdresser_data['phone_number'],
            email=hairdresser_data['email']
        )
    
    def get_appointment_treatments(self, appointment_id: int) -> list[AppointmentTreatment]:
        appointment_treatments_data = self.appointment_treatments[self.appointment_treatments['appointment_id'] == appointment_id]
        return [AppointmentTreatment(
            appointment_id=row['appointment_id'],
            treatment_id=row['treatment_id']
        ) for _, row in appointment_treatments_data.iterrows()]
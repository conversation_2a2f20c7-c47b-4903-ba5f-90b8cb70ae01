#!/usr/bin/env python3
"""
Script to synchronize LAA records with Contact records.
Matches LAA.Website with Contact.AgencyUrl and updates Contact.LAAId with the correct LAA._id.
"""

import sys
import os
from urllib.parse import urlparse
from typing import Dict, List, Optional
from datetime import datetime, UTC

# Add src to path to import modules
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.repositories.laarepository import LAARepository, LAARepositoryError
from src.repositories.contactrepository import ContactRepository, ContactRepositoryError
from src.models.laamodels import LAAgency
from src.models.contactmodels import ContactDetail
from bson import ObjectId


def normalize_url(url: str) -> str:
    """
    Normalize URL for consistent matching.
    Removes protocol, www, trailing slashes, and converts to lowercase.
    """
    if not url:
        return ""
    
    url = url.lower().strip()
    
    # Remove protocol
    if url.startswith(('http://', 'https://')):
        url = url.split('://', 1)[1]
    
    # Remove www.
    if url.startswith('www.'):
        url = url[4:]
    
    # Remove trailing slash
    url = url.rstrip('/')
    
    return url


def create_laa_lookup(laa_records: List[LAAgency]) -> Dict[str, LAAgency]:
    """
    Create a lookup dictionary mapping normalized website URLs to LAAgency objects.
    """
    lookup = {}
    duplicates = []
    
    for laa in laa_records:
        if not laa.Website:
            continue
            
        normalized_url = normalize_url(laa.Website)
        if not normalized_url:
            continue
            
        if normalized_url in lookup:
            duplicates.append(f"Duplicate URL found: {normalized_url} for LAA: {laa.LAAName} and {lookup[normalized_url].LAAName}")
        else:
            lookup[normalized_url] = laa
    
    if duplicates:
        print("Warning: Found duplicate URLs:")
        for dup in duplicates:
            print(f"  {dup}")
        print()
    
    return lookup


def sync_laa_contacts():
    """
    Main function to synchronize LAA records with Contact records.
    """
    print("Starting LAA-Contact synchronization...")
    print("=" * 50)
    
    try:
        # Initialize repositories
        print("Initializing repositories...")
        laa_repo = LAARepository()
        contact_repo = ContactRepository()
        
        # Get all LAA records
        print("Fetching all LAA records...")
        laa_records = laa_repo.get_agencies()
        print(f"Found {len(laa_records)} LAA records")
        
        # Create lookup dictionary
        print("Creating LAA lookup dictionary...")
        laa_lookup = create_laa_lookup(laa_records)
        print(f"Created lookup for {len(laa_lookup)} unique websites")
        
        # Get all contact records
        print("Fetching all contact records...")
        contact_records = contact_repo.get_contacts()
        print(f"Found {len(contact_records)} contact records")
        
        # Analyze potential duplicates before processing
        print("\nAnalyzing potential duplicate LAAId assignments...")
        laa_to_contacts = {}
        for contact in contact_records:
            if not contact.AgencyUrl:
                continue
            normalized_contact_url = normalize_url(contact.AgencyUrl)
            matching_laa = laa_lookup.get(normalized_contact_url)
            if matching_laa:
                if matching_laa._id not in laa_to_contacts:
                    laa_to_contacts[matching_laa._id] = []
                laa_to_contacts[matching_laa._id].append(contact)

        duplicate_scenarios = {laa_id: contacts for laa_id, contacts in laa_to_contacts.items() if len(contacts) > 1}
        if duplicate_scenarios:
            print(f"⚠️  Found {len(duplicate_scenarios)} LAA IDs that would be assigned to multiple contacts:")
            for laa_id, contacts in duplicate_scenarios.items():
                laa_name = next((laa.LAAName for laa in laa_records if laa._id == laa_id), "Unknown")
                print(f"   LAA '{laa_name}' ({laa_id}) -> {len(contacts)} contacts:")
                for contact in contacts:
                    print(f"     - {contact.AgencyName} (ID: {contact._id})")
            print()

        # Process matches and updates
        print("Processing matches and updates...")
        print("-" * 30)
        
        matched_count = 0
        updated_count = 0
        no_match_count = 0
        error_count = 0
        
        for i, contact in enumerate(contact_records):
            if (i + 1) % 100 == 0:
                print(f"Processed {i + 1}/{len(contact_records)} contacts...")
            
            if not contact.AgencyUrl:
                no_match_count += 1
                continue
            
            normalized_contact_url = normalize_url(contact.AgencyUrl)
            if not normalized_contact_url:
                no_match_count += 1
                continue
            
            # Find matching LAA
            matching_laa = laa_lookup.get(normalized_contact_url)
            if not matching_laa:
                no_match_count += 1
                continue
            
            matched_count += 1
            
            # Check if LAAId needs updating
            if contact.LAAId == matching_laa._id:
                continue  # Already has correct LAAId
            
            try:
                # Check if another contact already has this LAAId
                existing_contact = contact_repo.collection.find_one({"LAAId": matching_laa._id})

                if existing_contact and existing_contact['_id'] != contact._id:
                    # Handle duplicate LAAId scenario
                    print(f"⚠️  Duplicate LAAId conflict for '{contact.AgencyName}':")
                    print(f"   LAA ID {matching_laa._id} already assigned to contact ID: {existing_contact['_id']}")
                    print(f"   Existing contact: {existing_contact.get('AgencyName', 'Unknown')}")
                    print(f"   Current contact: {contact.AgencyName}")
                    print(f"   Skipping update to avoid constraint violation.\n")
                    error_count += 1
                    continue

                # Update the contact's LAAId directly in the database
                result = contact_repo.collection.update_one(
                    {"_id": contact._id},
                    {
                        "$set": {
                            "LAAId": matching_laa._id,
                            "DateModified": datetime.now(UTC)
                        }
                    }
                )

                if result.modified_count > 0:
                    updated_count += 1
                    print(f"✅ Updated contact '{contact.AgencyName}' -> LAA ID: {matching_laa._id}")

            except Exception as e:
                error_count += 1
                print(f"❌ Error updating contact '{contact.AgencyName}': {str(e)}")
        
        # Print summary
        print("\n" + "=" * 50)
        print("SYNCHRONIZATION SUMMARY")
        print("=" * 50)
        print(f"Total contacts processed: {len(contact_records)}")
        print(f"Contacts matched with LAA: {matched_count}")
        print(f"Contacts updated: {updated_count}")
        print(f"Contacts with no match: {no_match_count}")
        print(f"Errors encountered: {error_count}")
        print(f"Success rate: {(matched_count / len(contact_records) * 100):.1f}%")
        
        if updated_count > 0:
            print(f"\n✅ Successfully updated {updated_count} contact records!")
        else:
            print(f"\n✅ All contacts already have correct LAAId assignments!")
            
    except (LAARepositoryError, ContactRepositoryError) as e:
        print(f"Repository error: {str(e)}")
        sys.exit(1)
    except Exception as e:
        print(f"Unexpected error: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    sync_laa_contacts()

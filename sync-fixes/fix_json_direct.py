import json
import codecs

def fix_json_file(input_file, output_file):
    # Read the file as binary to avoid any encoding issues
    with open(input_file, 'rb') as f:
        binary_data = f.read()
    
    # Try different encodings to decode the binary data
    encodings = ['utf-8', 'latin-1', 'cp1252']
    
    for encoding in encodings:
        try:
            # Decode the binary data with the current encoding
            text_data = binary_data.decode(encoding)
            
            # Try to parse the JSON
            data = json.loads(text_data)
            
            # If successful, write the fixed JSON
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            
            print(f"Successfully fixed JSON using {encoding} encoding")
            return True
        except (UnicodeDecodeError, json.JSONDecodeError) as e:
            print(f"Failed with {encoding}: {e}")
    
    print("Failed to fix the JSON file with all attempted encodings")
    return False

# Run the function
input_file = "result-data/results_0-2226.json"
output_file = "result-data/results_0-2226_fixed.json"
fix_json_file(input_file, output_file)
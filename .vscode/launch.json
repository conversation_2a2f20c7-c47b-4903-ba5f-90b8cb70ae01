{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "name": "Python Debugger: Current File",
            "type": "debugpy",
            "request": "launch",
            "program": "${file}",
            "console": "integratedTerminal",
            "env": {
                "CONTACT_COLLECTION": "ContactCollection",
                "DB_NAME": "laadb",
                "LAA_COLLECTION": "LAACollection",
                "MONGO_CONNECTION": "mongodb://localhost:27017/",
                "OPENAI_API_KEY": "********************************************************************************************************************************************************************"
            }
        },
        {
            "name": "Python: AFTA API",
            "type": "debugpy",
            "request": "launch",
            "program": "${workspaceFolder}/src/main.py",
            "console": "integratedTerminal",
            "cwd": "${workspaceFolder}",
            "env": {
                "CONTACT_COLLECTION": "ContactCollection",
                "DB_NAME": "laadb",
                "LAA_COLLECTION": "LAACollection",
                "MONGO_CONNECTION": "mongodb://localhost:27017/",
                "OPENAI_API_KEY": "********************************************************************************************************************************************************************",
                "PYTHONPATH": "${workspaceFolder}"
            }
        }
    ]
}
